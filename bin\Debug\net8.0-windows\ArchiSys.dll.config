﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>


    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="ArchiSys.My.MySettings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <userSettings>
        <ArchiSys.My.MySettings>
            <setting name="ServerName" serializeAs="String">
                <value>LAPTOP-5491</value>
            </setting>
            <setting name="DataBaseName" serializeAs="String">
                <value>ArchiSys</value>
            </setting>
            <setting name="LoginID" serializeAs="String">
                <value />
            </setting>
            <setting name="LogPassword" serializeAs="String">
                <value />
            </setting>
            <setting name="LoginMetod" serializeAs="String">
                <value>1</value>
            </setting>
            <setting name="FilePath" serializeAs="String">
                <value>C:\</value>
            </setting>
            <setting name="Numérisationauto" serializeAs="String">
                <value />
            </setting>
            <setting name="PDFFolderPath" serializeAs="String">
                <value />
            </setting>
            <setting name="DefualtImgName" serializeAs="String">
                <value />
            </setting>
            <setting name="ImageFormat" serializeAs="String">
                <value>0</value>
            </setting>
            <setting name="ImageFolderPath" serializeAs="String">
                <value />
            </setting>
        </ArchiSys.My.MySettings>
    </userSettings>
</configuration>
