Imports System.Drawing.Imaging
Imports System.IO
Imports System.Windows.Forms
Imports ALquhaliLibrary
Imports Microsoft.Data.SqlClient



Public Class ProjectClass
    Public Sub DeletedSuccessfully(ByVal lbl As Label, ByVal pic As PictureBox, ByVal T As Timer)

        lbl.Text = "تم الحذف بنجاح!"
        lbl.ForeColor = Color.Green ' تحتاج Imports System.Drawing في أعلى ProjectClass.vb
        pic.Visible = True
        T.Interval = 2000 ' 2000 مللي ثانية = 2 ثانية
        T.Enabled = True  ' بدء المؤقت


        ' Private Sub Timer1_Tick(sender As Object, e As EventArgs) Handles Timer1.Tick
        '    Me.lblConfirmMsg.Text = ""
        '    Me.PicMsg.Visible = False
        '    Me.Timer1.Enabled = False
        ' End Sub
    End Sub



#Region "UserAcounts"


    'CommandBuilder Class

    'Clear Controls Data
    Public Sub ClearControls_FrmUsers(ByVal xx As FrmUserAccounts)

        xx.UserAccID.Text = GetNewID("UserAccID", "UserAccounts")
        xx.UserAccName.Clear()
        xx.UserName.Clear()
        xx.UserPassword.Clear()
        xx.AccType.Text = Nothing
        xx.AccActivate.Checked = False
        xx.UserImage.Image = Nothing
        xx.txtNavegate.Text = Nothing
        xx.btnSave.Enabled = True
        xx.btnEdit.Enabled = False
        xx.btnDelete.Enabled = False
        xx.UserAccName.Focus()

    End Sub

    ' Get New ID
    Public Function GetNewID(ByVal ColumnName As String, ByVal TableName As String)

        Dim dt As New DataTable
        dt.Clear()
        Dim da As New SqlDataAdapter
        da = New SqlDataAdapter("select MAX(" & ColumnName & ") from " & TableName & "", Con)
        da.Fill(dt)

        Dim MyNewID As Int64
        If IsDBNull(dt(0)(0)) = True Then
            MyNewID = 1
        Else
            MyNewID = dt(0)(0) + 1
        End If

        Return MyNewID
        da.Dispose()
        dt.Dispose()
    End Function

    'Save Users Acounts Data
    Public Sub Save_In_UsersTbl(ByVal xx As FrmUserAccounts)
        'Try
        If MyTextNull(xx.UserAccName, "اسم الحساب") = True Then Exit Sub
        If MyTextNull(xx.UserName, "اسم المستخدم") = True Then Exit Sub
        If MyTextNull(xx.UserPassword, "كلمة المرور") = True Then Exit Sub
        If MyCombIndexNull(xx.AccType, "نوع الحساب") = True Then Exit Sub

        Dim dt As New DataTable
        dt.Clear()
        Dim da As New SqlDataAdapter
        Dim sqlstr As String
        sqlstr = "Select * FROM UserAccounts where UserAccID = " & xx.UserAccID.Text & " "
        da = New SqlDataAdapter(sqlstr, Con)
        da.Fill(dt)

        If dt.Rows.Count = 0 Then

            dt.Rows.Add()
            Dim last As Integer = dt.Rows.Count - 1

            dt.Rows(last).Item("UserAccID") = xx.UserAccID.Text
            dt.Rows(last).Item("UserAccName") = xx.UserAccName.Text
            dt.Rows(last).Item("UserName") = xx.UserName.Text
            dt.Rows(last).Item("UserPassword") = xx.UserPassword.Text
            dt.Rows(last).Item("AccType") = xx.AccType.SelectedIndex
            dt.Rows(last).Item("AccActivate") = xx.AccActivate.Checked
            dt.Rows(last).Item("AddUser") = 1
            SavePicture(xx.UserImage, dt, last, "UserImage")
            RowInsert(dt, da)
            xx.btnNew.PerformClick()

            xx.lblConfirmMessage.Text = "تم الحفظ بنجاح"
            xx.PicMessage.Image = My.Resources.OK

            xx.Timer1.Start()

        End If

        'Catch ex As Exception
        '    MsgBox(ex.Message)
        '    Exit Sub
        'End Try

    End Sub

    'To get Image to Picter Box
    Public Sub GetImageTo_PictureBox(ByVal Pic As PictureBox)
        Dim ofd As New OpenFileDialog With {
            .Title = "إختر صورة",
            .Filter = "Choose Image (*.PNG;*.JPG;*.GIF;*.JPEG)|*.PNG;*.JPG;*.GIF;*.JPEG"
        }

        If ofd.ShowDialog() = DialogResult.OK Then
            img = 1
            Pic.SizeMode = PictureBoxSizeMode.StretchImage
            Pic.Load(ofd.FileName)
        End If
    End Sub

    'Save Picteurs in database
    Public Sub SavePicture(ByVal Pic As PictureBox, ByVal dt As DataTable, ByVal RowIndx As Integer, ByVal ImgCol As String)

        If Pic.Image Is Nothing Then
            dt.Rows(RowIndx).Item("" & ImgCol & "") = DBNull.Value
            img = 0
            Exit Sub
        End If

        Dim ImgArray() As Byte
        Using stream As New MemoryStream()
            Pic.Image.Save(stream, ImageFormat.Jpeg)
            ImgArray = stream.ToArray()
            dt.Rows(RowIndx).Item("" & ImgCol & "") = ImgArray
        End Using

        img = 0 ' إعادة تعيين العلم
    End Sub

    'Show Image in PictureBox
    Public Sub ShowSavedPicture(ByVal Pic As PictureBox, ByVal dt As DataTable, ByVal RowIndx As Integer, ByVal ColImage As String)

        If IsDBNull(dt.Rows(RowIndx).Item("" & ColImage & "")) = False Then
            Dim imgArray() As Byte
            imgArray = CType(dt.Rows(RowIndx).Item("" & ColImage & ""), Byte())

            Dim stream As New MemoryStream(imgArray)
            Pic.Image = Image.FromStream(stream)
        Else
            Pic.Image = Nothing
        End If

    End Sub



    ' Check username exestance in database
    Public Sub CheckExistanceOFName(ByVal TXT As TextBox)
        If TXT.Text.Trim = Nothing Then
            Exit Sub
        Else
            Dim dt As New DataTable
            Dim sqlstr As String
            sqlstr = "SELECT UserName FROM UserAccounts WHERE UserName = N'" & TXT.Text.Trim & "'"
            FillDataTable(dt, sqlstr)

            If dt.Rows.Count > 0 Then
                MessageBox.Show("موجود من قبل  """ & TXT.Text & """ اسم المستخدم", "رسالة تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information)
                TXT.Text = Nothing
                TXT.Focus()
            End If
        End If
    End Sub


    ' Show Users Accounts details
    Public Sub Show_UsersAccount_Data(ByVal xx As FrmUserAccounts, ByVal AccID As String)
        Try
            Dim DT As New DataTable
            DT.Clear()

            Dim sqlstr As String
            sqlstr = "SELECT * FROM UserAccounts WHERE (UserAccID = " & AccID & ")"

            FillDataTable(DT, sqlstr)

            If DT.Rows.Count > 0 Then
                xx.UserAccID.Text = DT.Rows(0).Item("UserAccID")
                xx.UserAccName.Text = DT.Rows(0).Item("UserAccName")
                xx.UserName.Text = DT.Rows(0).Item("UserName")
                xx.UserPassword.Text = DT.Rows(0).Item("UserPassword")
                xx.AccType.SelectedIndex = DT.Rows(0).Item("AccType")
                xx.AccActivate.Checked = DT.Rows(0).Item("AccActivate")

                ' عرض الصورة المخزنة
                ShowSavedPicture(xx.UserImage, DT, 0, "UserImage")

                ' تفعيل وتعطيل الأزرار
                xx.btnSave.Enabled = False
                xx.btnEdit.Enabled = True
                xx.btnDelete.Enabled = True
                DT.Dispose()
            End If


        Catch ex As Exception
            MsgBox(ex.Message, MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    'Update Users Acounts Data
    Public Sub update_In_UsersTbl(ByVal xx As FrmUserAccounts)
        'Try
        If MyTextNull(xx.UserAccName, "اسم الحساب") = True Then Exit Sub
        If MyTextNull(xx.UserName, "اسم المستخدم") = True Then Exit Sub
        If MyTextNull(xx.UserPassword, "كلمة المرور") = True Then Exit Sub
        If MyCombIndexNull(xx.AccType, "نوع الحساب") = True Then Exit Sub

        Dim dt As New DataTable
        dt.Clear()
        Dim da As New SqlDataAdapter
        Dim sqlstr As String
        sqlstr = "Select * FROM UserAccounts where UserAccID = " & xx.UserAccID.Text & " "
        da = New SqlDataAdapter(sqlstr, Con)
        da.Fill(dt)

        If dt.Rows.Count > 0 Then


            dt.Rows(0).Item("UserAccName") = xx.UserAccName.Text
            dt.Rows(0).Item("UserName") = xx.UserName.Text
            dt.Rows(0).Item("UserPassword") = xx.UserPassword.Text
            dt.Rows(0).Item("AccType") = xx.AccType.SelectedIndex
            dt.Rows(0).Item("AccActivate") = xx.AccActivate.Checked
            dt.Rows(0).Item("AddUser") = LogUserID
            dt.Rows(0).Item("EditDate") = Date.Now

            SavePicture(xx.UserImage, dt, 0, "UserImage")

            RowInsert(dt, da)

            xx.btnNew.PerformClick()

            xx.lblConfirmMessage.Text = " تم تعديل المعلومات بنجاح "
            xx.PicMessage.Image = My.Resources.OK

            xx.Timer1.Start()

        End If

        'Catch ex As Exception
        '    MsgBox(ex.Message)
        '    Exit Sub
        'End Try

    End Sub

#End Region

#Region "Folders"
    'Clear FrmFolder Controls
    Public Sub ClearFrmAddFoldersControls(ByVal xx As FrmAddFolders)

        xx.FolderNumber.Text = GetFolderNumber(xx, xx.FolderParent.SelectedValue)
        xx.FolderName.Clear()
        xx.FolderID.Clear()
        xx.FolderParent.Text = Nothing

        xx.btnSave.Enabled = True
        xx.btnEdit.Enabled = False
        xx.btnDelete.Enabled = False
        xx.FolderName.Focus()

    End Sub

    'Get New Folder Number
    Public Function GetFolderNumber(ByVal xx As FrmAddFolders, ByVal CmbValue As String) As Decimal
        Dim dt As New DataTable
        Dim SqlSt As String

        If xx.FolderParent.Text = String.Empty Then
            SqlSt = "select MAX(FolderNumber) from Folders where FolderParent = " & 0 & " "
            FillDataTable(dt, SqlSt)
        ElseIf xx.FolderParent.SelectedValue >= 0 Then
            SqlSt = "select MAX(FolderNumber) from Folders where FolderParent = '" & CmbValue & "' "
            FillDataTable(dt, SqlSt)
        End If

        If IsDBNull(dt(0)(0)) = True Then
            Return CmbValue & 0 & 0 & 1
        Else
            Return dt(0)(0) + 1
        End If

        dt.Dispose()
    End Function

    ' Fill Combobox by Folders
    Public Sub FillMyComboBox(ByVal Combo As ComboBox)
        Dim SqlStr As String = "SELECT FolderNumber, FolderName FROM Folders WHERE IsDeleted='False'"
        Dim DT As New DataTable
        FillDataTable(DT, SqlStr)
        If DT.Rows.Count > 0 Then
            Combo.DataSource = DT
            Combo.DisplayMember = "FolderName"
            Combo.ValueMember = "FolderNumber"
            Combo.Text = Nothing
        End If
    End Sub



    ' Save folders using stored procedure InsertFolder
    Public Sub Save_Folders(frm As FrmAddFolders, FolderNumber As Decimal, FolderName As String, FolderParent As Decimal, AddUser As Short)

        Dim Cmd As New SqlCommand
        Cmd = New SqlCommand("InsertFolder", Con)
        Cmd.CommandType = CommandType.StoredProcedure

        Dim Parameter(3) As SqlParameter

        Parameter(0) = New SqlParameter("@FolderNumber", SqlDbType.Decimal, 25)
        Parameter(0).Value = FolderNumber

        Parameter(1) = New SqlParameter("@FolderName", SqlDbType.VarChar, 100)
        Parameter(1).Value = FolderName

        Parameter(2) = New SqlParameter("@FolderParent", SqlDbType.Decimal, 25)
        Parameter(2).Value = FolderParent

        Parameter(3) = New SqlParameter("@AddUser", SqlDbType.TinyInt)
        Parameter(3).Value = AddUser

        Cmd.Parameters.AddRange(Parameter)

        OpenConnection()
        Cmd.ExecuteNonQuery()
        CloseConnection()
        AddFolderToTreeView(FrmMainPage.TreeView1, FolderName, CStr(FolderParent), CStr(FolderNumber), FrmMainPage.lblCount)

        frm.btnNew.PerformClick()
        ConfirmMessage(frm.lblConfirmMessage, frm.PicMessage, frm.Timer1, "تم الحفظ بنجاح")
    End Sub

    Dim Dt As New DataTable
    'تعبئة شجرة TreeView بالمجلدات من جدول Folders
    Public Sub FillTreeView(ByVal key As String, ByVal txt As String, ByVal N As TreeNode, ByVal ImageIndex As Int16, ByVal TV As TreeView)
        Dim NN As TreeNode

        If N Is Nothing Then
            NN = TV.Nodes.Add(key, txt, ImageIndex, ImageIndex)
            NN.Tag = key
        Else
            NN = N.Nodes.Add(key, txt, ImageIndex, 10)
            NN.Tag = key
        End If


        Dim dv As DataView = Dt.DefaultView
        Dim xkey As Int64 = Convert.ToInt64(key)
        dv.RowFilter = "FolderParent = " & xkey & ""

        For Each dr As DataRow In dv.ToTable.Rows
            FillTreeView(dr("FolderNumber").ToString(), dr("FolderName").ToString(), NN, CInt(dr("FolderImageIndex")), TV)
        Next

        Dt.Dispose()
    End Sub

    'Show folders details
    Public Sub Show_Folders_Data(ByVal xx As FrmAddFolders, ByVal ID As String)
        Try
            Dim DT As New DataTable
            Dim sqlstr As String
            sqlstr = "Select FolderID, FolderNumber, FolderName, FolderParent FROM Folders WHERE FolderNumber = " & ID & ""
            FillDataTable(DT, sqlstr)

            If DT.Rows.Count > 0 Then
                xx.FolderID.Text = DT.Rows(0).Item("FolderID")
                xx.FolderName.Text = DT.Rows(0).Item("FolderName")
                xx.FolderParent.SelectedValue = DT.Rows(0).Item("FolderParent")
                xx.FolderNumber.Text = DT.Rows(0).Item("FolderNumber")

                xx.btnSave.Enabled = False
                xx.btnEdit.Enabled = True
                xx.btnDelete.Enabled = True
            End If

            DT.Dispose()
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
    End Sub

    ' Update folders
    Public Sub Update_Folders(ByVal xx As FrmAddFolders, ByVal FolderId As Int32, ByVal FolderNumber As Decimal, ByVal FolderName As String, ByVal FolderParent As Decimal, ByVal EditUser As Int16)

        Dim Cmd As SqlCommand
        Cmd = New SqlCommand("UpdateFolders", Con)
        Cmd.CommandType = CommandType.StoredProcedure

        Dim Parameter(4) As SqlParameter

        Parameter(0) = New SqlParameter("@FolderNumber", SqlDbType.Decimal, 25)
        Parameter(0).Value = FolderNumber

        Parameter(1) = New SqlParameter("@FolderName", SqlDbType.VarChar, 100)
        Parameter(1).Value = FolderName

        Parameter(2) = New SqlParameter("@FolderParent", SqlDbType.Decimal, 25)
        Parameter(2).Value = FolderParent

        Parameter(3) = New SqlParameter("@EditUser", SqlDbType.TinyInt)
        Parameter(3).Value = EditUser

        Parameter(4) = New SqlParameter("@FolderID", SqlDbType.Int)
        Parameter(4).Value = FolderId

        Cmd.Parameters.AddRange(Parameter)

        OpenConnection()
        Cmd.ExecuteNonQuery()
        CloseConnection()
        UpdateNodeInTreeView(FrmMainPage.TreeView1, FolderName, CStr(FolderNumber))
        xx.btnNew.PerformClick()  ' Clear Controls
        ConfirmMessage(xx.lblConfirmMessage, xx.PicMessage, xx.Timer1, "تم تعديل المجلد بنجاح")

    End Sub


    ' تحميل شجرة الأرشيف داخل TreeView

    Public Sub LoadArchiveTree(ByVal xx As FrmMainPage)
        Dt = GetDataTable("SelectArchiveTree", Nothing)

        xx.TreeView1.BeginUpdate()
        xx.TreeView1.Nodes.Clear()

        ' إضافة الجذر الرئيسي للشجرة
        FillTreeView("0", "شجرة الأرشيف", Nothing, 0, xx.TreeView1)

        xx.TreeView1.TopNode.Expand()
        xx.TreeView1.TopNode.NodeFont = New Font("Times New Roman", 18.25!, FontStyle.Bold)
        xx.TreeView1.TopNode.ForeColor = Color.Blue
        xx.TreeView1.Select()
        xx.TreeView1.EndUpdate()

        xx.lblCount.Text = xx.TreeView1.TopNode.GetNodeCount(True)
        xx.txtSearch.Focus()
    End Sub

    ' Delete Folders
    Public Function DeleteFolders(ByVal FolderNumber As String) As Int32
        Dim Paramter(0) As SqlParameter

        Paramter(0) = New SqlParameter("@FolderNumber", SqlDbType.Decimal)
        Paramter(0).Value = CDec(FolderNumber)

        Dim dt As New DataTable
        dt = GetDataTable("CheckAndDeleteFolders", Paramter)

        Return dt.Rows(0).Item("Result")
    End Function

    'Add Folder To TreeView
    Public Sub AddFolderToTreeView(ByVal Tv As TreeView, ByVal FolderName As String, ByVal ParentFolder As String, ByVal FolderNumber As String, ByVal lbl As Label)

        If Tv.Nodes.Count = 0 Then Exit Sub
        Dim TN As TreeNode
        Dim xFind() As TreeNode = Tv.Nodes.Find(ParentFolder, True)
        Tv.SelectedNode = xFind(0)
        Tv.SelectedNode.Expand()

        Dim index As Int16 = Tv.SelectedNode.ImageIndex + 1
        TN = Tv.SelectedNode.Nodes.Add(FolderNumber, FolderName, index, 10)

        TN.Tag = FolderNumber
        Tv.SelectedNode = TN

        lbl.Text = Val(lbl.Text) + 1

    End Sub


    'Update Node in TreeView
    Public Sub UpdateNodeInTreeView(ByVal Tv As TreeView, ByVal NodName As String, ByVal NodId As String)

        If Tv.Nodes.Count = 0 Then Exit Sub
        Tv.SelectedNode.Text = NodName
        Tv.SelectedNode.Tag = NodId

    End Sub






#End Region

#Region "Files"
    'Clear File form Controls
    Public Sub ClearFrmAddFilesControls(ByVal xx As FrmAddFiles)

        Clear_Control_On_TabControl(xx.TabControl1)
        xx.AddUser.Text = LogUserName
        xx.AddDate.Text = Now.ToString("dd-MM-yyyy hh:mm tt")
        xx.dgv.Rows.Clear()
        If xx.WebView21.CoreWebView2 IsNot Nothing Then
            xx.WebView21.CoreWebView2.Navigate("about:blank")
        End If
        xx.WebView21.Visible = False
        xx.btnScan.Enabled = True
        xx.btnBrows.Enabled = True
        xx.btnSave.Enabled = True
        xx.btnEdit.Enabled = False
        xx.btnDelete.Enabled = False
        xx.btnSaveAttaches.Enabled = False
        xx.FolderParent.Focus()

    End Sub


    Public Sub Clear_Control_On_TabControl(ByVal TabControl As TabControl)

        TabControl.Enabled = True

        For i As Int16 = 0 To TabControl.TabPages.Count - 1
            For x As Int16 = 0 To TabControl.TabPages(i).Controls.Count - 1

                ' clear TextBox Controls
                If TypeOf TabControl.TabPages(i).Controls(x) Is TextBox Then
                    Dim TXT As TextBox = TabControl.TabPages(i).Controls(x)
                    TXT.Clear()
                End If

                ' Clear Combobox Control
                If TypeOf TabControl.TabPages(i).Controls(x) Is ComboBox Then
                    Dim Combo As ComboBox = TabControl.TabPages(i).Controls(x)
                    Combo.SelectedValue = 0
                    Combo.SelectedIndex = -1
                    Combo.Text = Nothing
                End If

                ' Clear CheckBox Control
                If TypeOf TabControl.TabPages(i).Controls(x) Is CheckBox Then
                    Dim Ck As CheckBox = TabControl.TabPages(i).Controls(x)
                    Ck.Checked = False
                End If

            Next
        Next

    End Sub

    'Git File Code
    Public Function GetFileCode(ByVal CmbValue As Decimal) As Decimal

        Dim Paramter(0) As SqlParameter

        Paramter(0) = New SqlParameter("@FolderParent", SqlDbType.Decimal, 25)
        Paramter(0).Value = CmbValue

        Dim dt As New DataTable
        dt = GetDataTable("[GetFileCode]", Paramter) ' Assuming GetDataTablet is a custom function to execute a stored procedure
        Return dt(0)(0) ' This line might need adjustment based on the actual return type of your stored procedure and GetDataTablet function
        dt.Dispose() ' This line is unreachable as it's after a Return statement

    End Function


    'Git File Info
    Public Sub GetMainFileInfo(ByVal xx As FrmAddFiles)
        If MyCombIndexNull(xx.FolderParent, "المجلد الاب أولا") Then Return
        Dim OldFilePath, NewFilePath, StoredPath As String
        Dim Ofd As New OpenFileDialog
        Ofd.Title = "اختر الملف"
        If Ofd.ShowDialog = DialogResult.OK Then
            OldFilePath = Ofd.FileName
        Else
            Exit Sub
        End If

        StoredPath = My.Settings.FilePath & "\Archived Files\" & xx.FolderParent.SelectedValue

        ' Check if Stored folder Exist
        If My.Computer.FileSystem.FileExists(StoredPath) = True Then
            NewFilePath = StoredPath & "\" & Path.GetFileName(OldFilePath)
        Else
            Directory.CreateDirectory(StoredPath)
            NewFilePath = StoredPath & "\" & Path.GetFileName(OldFilePath)
        End If
        ' Check if the Selected File Exist
        If My.Computer.FileSystem.FileExists(NewFilePath) = True Then
            Dim Msg As String = "الملف الذي تم اختياره موجود مسبقاً في المجلد" & vbNewLine &
                                "..." & xx.FolderParent.Text & vbNewLine &
                                "...يمكنك تغيير اسم الملف، أو اختيار ملف آخر"
            If MessageBox.Show(Msg, "رسالة تنبيه", MessageBoxButtons.YesNo, MessageBoxIcon.Information) = DialogResult.Yes Then
                GetMainFileInfo(xx) ' استدعاء ذاتي للإجراء (recursion)
            End If
        Else
            xx.FileName.Text = Path.GetFileNameWithoutExtension(OldFilePath)
            xx.FileExtenssion.Text = Path.GetExtension(OldFilePath).ToLower
            xx.FileSize.Text = GetFileSize(OldFilePath)
            xx.FileType.SelectedIndex = GetFileIcon(xx.FileExtenssion.Text)
            xx.FileDescription.Tag = OldFilePath
            xx.RefrenceNum.Tag = StoredPath
            ' ** هنا التغيير: اجعل WebView2 مرئيًا **
            xx.WebView21.Visible = True ' جعل WebView2 مرئيا لعرض الملف

            ' ** والأهم: تأكد من تهيئة CoreWebView2 قبل محاولة التنقل **
            If xx.WebView21.CoreWebView2 IsNot Nothing Then
                ' قم بتحميل الملف في WebView2
                ' استخدم Navigate() لـ CoreWebView2
                xx.WebView21.CoreWebView2.Navigate(New Uri(OldFilePath).AbsoluteUri)
            Else
                ' إذا لم يتم تهيئة CoreWebView2، يمكنك إظهار رسالة أو الانتظار.
                ' الخطأ "لم يتم تهيئته بعد WebView2 متصفح" يشير إلى هذه المشكلة.
                ' سنعالج هذا بشكل أفضل في الخطوة 2.
                MessageBox.Show("متصفح WebView2 لم يتم تهيئته بعد. يرجى الانتظار أو إعادة المحاولة.", "خطأ في WebView2", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            End If

            ' ** احذف سطر WebBrowser1 إذا كنت لا تستخدمه **
            ' إذا كان لديك فقط WebView2، يجب إزالة الأسطر المتعلقة بـ WebBrowser1.
            ' xx.WebBrowser1.Url = New Uri(OldFilePath) ' هذا السطر سيعطي خطأ إذا لم يكن WebBrowser1 موجودا.
            ' xx.WebBrowser1.Visible = False ' يجب إزالته أيضا.
        End If



    End Sub

    'Get Main File info - Second Overload
    Public Sub GetMainFileInfo(ByVal xx As FrmAddFiles, ByVal xFilePath As String)

        Dim OldFilePath, NewFilePath, StoredPath As String

        OldFilePath = xFilePath
        StoredPath = My.Settings.FilePath & "\Archived Files\" & xx.FolderParent.SelectedValue

        ' Check if Stored folder Exist
        If My.Computer.FileSystem.DirectoryExists(StoredPath) = False Then
            Directory.CreateDirectory(StoredPath)
        End If

        NewFilePath = StoredPath & "\" & Path.GetFileName(OldFilePath)

        ' Check if the Selected File Exist
        If My.Computer.FileSystem.FileExists(NewFilePath) = True Then
            Dim Msg As String = "الملف الذي تم اختياره موجود مسبقاً في المجلد" & vbCrLf &
                            "..." & xx.FolderParent.Text & vbCrLf &
                            "...يمكنك تغيير اسم الملف، أو اختيار ملف آخر"
            If MessageBox.Show(Msg, "رسالة تنبيه", MessageBoxButtons.YesNo, MessageBoxIcon.Information) = DialogResult.Yes Then
                GetMainFileInfo(xx, xFilePath) ' recursion call
            End If
        Else
            xx.FileName.Text = Path.GetFileNameWithoutExtension(OldFilePath)
            xx.FileExtenssion.Text = Path.GetExtension(OldFilePath).ToLower
            xx.FileSize.Text = GetFileSize(OldFilePath)
            xx.FileType.SelectedIndex = GetFileIcon(xx.FileExtenssion.Text)
            xx.FileDescription.Tag = OldFilePath
            xx.RefrenceNum.Tag = StoredPath

            ' عرض الملف في WebView2
            xx.WebView21.Visible = True
            If xx.WebView21.CoreWebView2 IsNot Nothing Then
                xx.WebView21.CoreWebView2.Navigate(New Uri(OldFilePath).AbsoluteUri)
            Else
                MessageBox.Show("متصفح WebView2 لم يتم تهيئته بعد. يرجى الانتظار أو إعادة المحاولة.", "WebView2", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            End If
        End If

    End Sub


    ' Get Attached Files Info
    Public Sub GetAttachedFileInfo(ByVal xx As FrmAddFiles, ByVal dgv As DataGridView)

        ' يتحقق مما إذا كان حقل xx.FileSecret.Text فارغًا
        If xx.FileSecret.Text.Trim = String.Empty Then
            MessageBox.Show("لا يمكن إضافة ملفات مرفقة قبل إضافة الملف الأساسي أولاً", "رسالة تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information)
            xx.TabControl1.SelectedTab = xx.TabPage1 ' يعود إلى علامة التبويب الأولى (البيانات الأساسية)
            Return ' يخرج من الإجراء الفرعي
        End If

        Dim OldFilePath As String ' لا تحتاج لـ XFileName هنا بعد الآن، ستستخدم Path.GetFileNameWithoutExtension مباشرة
        Dim Ofd As New OpenFileDialog
        Ofd.Title = "اختر الملف"

        If Ofd.ShowDialog = DialogResult.OK Then
            OldFilePath = Ofd.FileName
        Else
            ' ** إذا ألغى المستخدم مربع الحوار، اعرض رسالة تحذير ثم اخرج. **
            MessageBox.Show("لم يتم تحميل مسار الملف بشكل صحيح!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return ' ** هذا السطر ضروري جداً للخروج ومنع تنفيذ بقية الكود **
        End If

        ' ** من هذه النقطة فصاعدًا، نحن متأكدون أن OldFilePath تحتوي على مسار صالح. **

        ' Check if the Selected File Exist (في الـ DataGridView)
        Dim selectedFileNameWithoutExtension As String = Path.GetFileNameWithoutExtension(OldFilePath)
        Dim Msg As String = "الملف الذي تم اختياره موجود مسبقاً ضمن المرفقات"
        For i As Int16 = 0 To dgv.Rows.Count - 1
            ' يتحقق مما إذا كانت قيمة العمود 1 في الصف الحالي من الـ DataGridView
            ' تساوي اسم الملف بدون امتداد الذي تم اختياره
            If dgv(1, i).Value.ToString = selectedFileNameWithoutExtension Then
                MessageBox.Show(Msg, "رسالة تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information)
                dgv(1, i).Selected = True ' يحدد الصف الذي يحتوي على الملف الموجود
                Return ' يخرج من الإجراء الفرعي
            End If
        Next

        ' إذا لم يتم العثور على الملف في الـ DataGridView، يتم إضافته
        dgv.Rows.Add() ' يضيف صفًا جديدًا إلى الـ DataGridView
        Dim x As Integer = dgv.Rows.Count - 1 ' يحصل على فهرس الصف الجديد (آخر صف)

        dgv(0, dgv.Rows(x).Index).Value = 0 ' العمود 0: يعين القيمة 0 (ربما رقم تسلسلي أو حالة)
        dgv(1, dgv.Rows(x).Index).Value = selectedFileNameWithoutExtension ' العمود 1: اسم الملف بدون امتداد
        dgv(2, dgv.Rows(x).Index).Value = Path.GetExtension(OldFilePath).ToLower ' العمود 2: امتداد الملف (بأحرف صغيرة)
        dgv(3, dgv.Rows(x).Index).Value = GetFileSize(OldFilePath) ' العمود 3: حجم الملف (باستخدام الدالة GetFileSize)
        dgv(4, dgv.Rows(x).Index).Value = Now.ToString("dd-MM-yyyy hh:mm:ss tt") ' العمود 4: تاريخ ووقت الإضافة الحالي
        dgv(5, dgv.Rows(x).Index).Value = OldFilePath ' العمود 5: المسار الكامل للملف الأصلي

        ' ** عرض الملف في WebView2 **
        xx.WebView21.Visible = True ' جعل WebView2 مرئيًا
        xx.FilePathToLoad = OldFilePath ' تخزين مسار الملف في خاصية النموذج

        ' تحقق مما إذا كان CoreWebView2 مهيئًا بالفعل قبل محاولة التنقل
        If xx.WebView21.CoreWebView2 IsNot Nothing Then
            ' تأكد من أن FilePathToLoad ليست فارغة قبل محاولة إنشاء Uri
            If Not String.IsNullOrWhiteSpace(xx.FilePathToLoad) Then
                xx.WebView21.CoreWebView2.Navigate(New Uri(xx.FilePathToLoad).AbsoluteUri)
                ' لا تفرغ xx.FilePathToLoad هنا. يجب أن يفرغها حدث CoreWebView2InitializationCompleted بعد التحميل
            End If
        End If
        ' لا تضع رسالة الخطأ هنا، لأننا قد عالجنا حالة الإلغاء بالفعل في بداية الدالة
        ' ولا تفرغ xx.FilePathToLoad هنا مباشرة، دع WebView21_CoreWebView2InitializationCompleted يتعامل مع ذلك.

    End Sub


    'Get Attached Files info Second overload
    Public Sub GetAttachedFileInfo(ByVal xx As FrmAddFiles, ByVal FilePath As String)

        Dim OldFilePath, XFileName As String

        OldFilePath = FilePath
        XFileName = Path.GetFileNameWithoutExtension(OldFilePath).ToString

        ' Check if the Selected File Exist
        Dim Msg As String = "الملف الذي تم اختياره موجود مسبقاً ضمن المرفقات"
        For i As Int16 = 0 To xx.dgv.Rows.Count - 1
            If xx.dgv(1, i).Value.ToString = XFileName Then
                MessageBox.Show(Msg, "رسالة تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information)
                xx.dgv(1, i).Selected = True
                Return
            End If
        Next

        xx.dgv.Rows.Add()
        Dim x As Integer = xx.dgv.Rows.Count - 1
        xx.dgv(0, x).Value = 0
        xx.dgv(1, x).Value = XFileName
        xx.dgv(2, x).Value = Path.GetExtension(OldFilePath).ToLower
        xx.dgv(3, x).Value = GetFileSize(OldFilePath)
        xx.dgv(4, x).Value = Now.ToString("dd-MM-yyyy hh:mm:ss tt")
        xx.dgv(5, x).Value = OldFilePath

        ' عرض الملف في WebView2 بدل WebBrowser1
        xx.WebView21.Visible = True
        If xx.WebView21.CoreWebView2 IsNot Nothing Then
            xx.WebView21.CoreWebView2.Navigate(New Uri(OldFilePath).AbsoluteUri)
        Else
            MessageBox.Show("المتصفح WebView2 لم يتم تهيئته بعد!", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub


    Public Sub AddFilesFormFormatting(ByVal xx As FrmAddFiles)

        xx.btnScan.Enabled = False
        xx.btnBrows.Enabled = False
        xx.btnSave.Enabled = False
        xx.btnEdit.Enabled = False
        xx.btnDelete.Enabled = False
        Clear_Control_On_TabControl(xx.TabControl1)
        xx.TabControl1.Enabled = False

    End Sub


    Public Sub DeleteFromDatabase(ByVal TableName As String, ByVal Columnname As String, ByVal ID As String, ByVal lbl As Label, ByVal pic As PictureBox, ByVal T As Timer)
        Try
            Dim DT As New DataTable
            Dim da As New SqlDataAdapter
            da = New SqlDataAdapter("Delete from " & TableName & " where " & Columnname & " = " & ID & "", Con)
            da.Fill(DT)
            da.Dispose()
            DT.Dispose()
            DeletedSuccessfully(lbl, pic, T)
        Catch ex As Exception
            MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' Archive Created PDF

    Public Sub CreateAndArchivePDF(ByVal xx As FrmScan)
        Try
            ' التأكد من وجود صور
            If xx.CheckedListBox1.Items.Count = 0 Then Return

            ' التحقق من العناصر الفارغة داخل القائمة وإزالتها
            For i As Integer = xx.CheckedListBox1.Items.Count - 1 To 0 Step -1
                If xx.CheckedListBox1.Items(i) Is Nothing Then
                    xx.CheckedListBox1.Items.RemoveAt(i)
                End If
            Next

            ' التحقق مرة أخرى بعد التنظيف
            If xx.CheckedListBox1.Items.Count = 0 Then
                MessageBox.Show("لم يتم تحديد أي صور صالحة لتحويلها إلى PDF.", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            ' التحقق من اسم الملف
            If MyTextNull(xx.txtFileName, "اسم الملف الذي تريد حفظه") Then Return

            ' التحقق من مسار الحفظ
            If My.Settings.PDFFolderPath = String.Empty Then
                MessageBox.Show("اختر مسار حفظ ملفات الـ PDF من الإعدادات" & vbCrLf, "ملاحظة", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            ' إنشاء المسارات
            Dim CreatedFileFolder As String = My.Settings.PDFFolderPath
            Dim CreatedFilePath As String = CreatedFileFolder & "\" & xx.txtFileName.Text.Trim & ".pdf"

            ' إنشاء المجلد إذا لم يكن موجودًا
            If Not Directory.Exists(CreatedFileFolder) Then
                Directory.CreateDirectory(CreatedFileFolder)
            End If

            ' تحويل الصور إلى PDF
            TPro.ConvertImagesToPDF(xx.CheckedListBox1, CreatedFilePath)

            ' تأكيد الأرشفة
            If MessageBox.Show("هل تريد أرشفة الملف: " & xx.txtFileName.Text.Trim, "رسالة تنبيه", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                xx.Close()

                If ScanTo = 1 Then
                    GetMainFileInfo(FrmAddFiles, CreatedFilePath)
                ElseIf ScanTo = 2 Then
                    GetAttachedFileInfo(FrmAddFiles, CreatedFilePath)
                End If
            End If

        Catch ex As Exception
            MessageBox.Show(ex.Message, "Error Message", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
        End Try
    End Sub





    ' عرض الصورة في PictureBox2
    Public Shared Sub DisplayImagePreview(frm As FrmScan, filePath As String)
        Try
            If IO.File.Exists(filePath) Then
                frm.PictureBox2.Image = Image.FromFile(filePath)
                frm.PictureBox2.SizeMode = PictureBoxSizeMode.Zoom ' أو StretchImage حسب الرغبة
            End If
        Catch ex As Exception
            MessageBox.Show("حدث خطأ أثناء عرض الصورة!" & vbCrLf & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub




    Function CheckBeforeSave(ByVal xx As FrmAddFiles) As Boolean

        If MyTextNull(xx.FileCode, "رقم الملف") Then Return True
        If xx.FolderParent.SelectedValue Is Nothing Then
            MessageBox.Show("من فضلك اختر المجلد الاب للملف", "رساله تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information)
            xx.FolderParent.Focus()
            Return True
        End If
        If MyTextNull(xx.FileName, "اسم الملف") Then Return True
        If MyCombIndexNull(xx.FileType, "نوع الملف") Then Return True
        If xx.FileSecret.Checked = True And xx.FilePassword.Text.Trim = String.Empty Then xx.FileSecret.Checked = False
        Return False
    End Function


    Private Function BuildAttachedFilesTable(ByVal dgv As DataGridView) As DataTable

        Dim dt As New DataTable
        dt.Columns.Add("ParentID")
        dt.Columns.Add("FileName")
        dt.Columns.Add("FileExtenssion")
        dt.Columns.Add("FileSize")
        dt.Columns.Add("FileData", Type.GetType("System.Byte[]"))
        For i As Int16 = 0 To dgv.Rows.Count - 1
            dt.Rows.Add(0, dgv(1, i).Value, dgv(2, i).Value, dgv(3, i).Value, Nothing)
        Next
        Return dt
    End Function
    Public Sub Save_Files(ByVal xx As FrmAddFiles)
        Try
            If CheckBeforeSave(xx) = True Then Return

            Dim Cmd As SqlCommand
            Cmd = New SqlCommand("InsertFiles", Con)
            Cmd.CommandType = CommandType.StoredProcedure

            Dim Paramter(13) As SqlParameter ' زيادة عدد المعاملات لإضافة FilePath

            Paramter(0) = New SqlParameter("@FileCode", SqlDbType.Decimal, 28)
            Paramter(0).Value = CDec(xx.FileCode.Text)

            Paramter(1) = New SqlParameter("@FileName", SqlDbType.NVarChar, 300)
            Paramter(1).Value = xx.FileName.Text

            Paramter(2) = New SqlParameter("@FolderParent", SqlDbType.Decimal, 25)
            Paramter(2).Value = CDec(xx.FolderParent.SelectedValue)

            Paramter(3) = New SqlParameter("@RefrenceNum", SqlDbType.NVarChar)
            Paramter(3).Value = xx.RefrenceNum.Text

            Paramter(4) = New SqlParameter("@FileDescription", SqlDbType.NVarChar)
            Paramter(4).Value = xx.FileDescription.Text

            Paramter(5) = New SqlParameter("@FileExtenssion", SqlDbType.VarChar, 10)
            Paramter(5).Value = xx.FileExtenssion.Text

            Paramter(6) = New SqlParameter("@FileSize", SqlDbType.VarChar, 20)
            Paramter(6).Value = xx.FileSize.Text

            Paramter(7) = New SqlParameter("@FileType", SqlDbType.TinyInt)
            Paramter(7).Value = CShort(xx.FileType.SelectedIndex)

            Paramter(8) = New SqlParameter("@FileSecret", SqlDbType.Bit)
            Paramter(8).Value = CBool(xx.FileSecret.Checked)

            Paramter(9) = New SqlParameter("@FilePassword", SqlDbType.NVarChar, 25)
            Paramter(9).Value = xx.FilePassword.Text

            Paramter(10) = New SqlParameter("@AddUser", SqlDbType.TinyInt)
            Paramter(10).Value = LogUserID

            Paramter(11) = New SqlParameter("@DTAttaches", SqlDbType.Structured)
            Paramter(11).Value = BuildAttachedFilesTable(xx.dgv)

            ' إضافة معامل مسار الملف
            Paramter(12) = New SqlParameter("@FilePath", SqlDbType.NVarChar, 500)
            If xx.FileDescription.Tag IsNot Nothing AndAlso Not String.IsNullOrEmpty(xx.FileDescription.Tag.ToString()) Then
                ' حفظ المسار النسبي للملف المحفوظ
                Dim relativePath As String = "\Archived Files\" & xx.FolderParent.SelectedValue.ToString & "\" & Path.GetFileName(xx.FileDescription.Tag.ToString())
                Paramter(12).Value = relativePath
            Else
                Paramter(12).Value = DBNull.Value
            End If

            Paramter(13) = New SqlParameter("@ID", SqlDbType.Int)
            Paramter(13).Direction = ParameterDirection.Output

            Cmd.Parameters.AddRange(Paramter)
            OpenConnection()
            Cmd.ExecuteNonQuery()
            CloseConnection()

            If Paramter(13).Value > 0 Then
                xx.ReturnID = Paramter(13).Value

                ' نسخ الملفات بعد نجاح الحفظ
                CopySavedFile(xx, xx.ReturnID)

                ' عرض رسالة تأكيد
                ConfirmMessage(xx.lblConfirmMsg, xx.PicMsg, xx.Timer1, "تم الحفظ بنجاح")

                ' إعادة تهيئة النموذج
                xx.btnNew.PerformClick()
            End If

        Catch ex As Exception
            MessageBox.Show(ex.Message, "Error Message", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Public Sub CopySavedFile(ByVal xx As FrmAddFiles, ByVal FileID As String)
        Try
            Dim StoredPath As String
            StoredPath = My.Settings.FilePath & "\Archived Files\" & xx.FolderParent.SelectedValue.ToString

            ' إنشاء المجلد إذا لم يكن موجوداً
            If Not Directory.Exists(StoredPath) Then
                Directory.CreateDirectory(StoredPath)
            End If

            ' نسخ الملف الرئيسي
            If xx.FileDescription.Tag IsNot Nothing AndAlso Not String.IsNullOrEmpty(xx.FileDescription.Tag.ToString()) Then
                Dim sourceFilePath As String = xx.FileDescription.Tag.ToString()
                Dim destinationFilePath As String = StoredPath & "\" & Path.GetFileName(sourceFilePath)

                ' التحقق من وجود الملف المصدر
                If File.Exists(sourceFilePath) Then
                    ' حذف الملف الوجهة إذا كان موجوداً لتجنب خطأ النسخ
                    If File.Exists(destinationFilePath) Then
                        File.Delete(destinationFilePath)
                    End If

                    File.Copy(sourceFilePath, destinationFilePath)

                    ' تحديث مسار الملف في قاعدة البيانات
                    UpdateFilePath(FileID, "\Archived Files\" & xx.FolderParent.SelectedValue.ToString & "\" & Path.GetFileName(sourceFilePath))
                Else
                    MessageBox.Show("الملف المصدر غير موجود: " & sourceFilePath, "خطأ في نسخ الملف", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                End If
            End If

            ' نسخ الملفات المرفقة
            If xx.dgv.Rows.Count > 0 Then
                Dim attachedFilesPath As String = StoredPath & "\" & FileID
                If Not Directory.Exists(attachedFilesPath) Then
                    Directory.CreateDirectory(attachedFilesPath)
                End If

                For i As Int16 = 0 To xx.dgv.Rows.Count - 1
                    If xx.dgv(5, i).Value IsNot Nothing AndAlso Not String.IsNullOrEmpty(xx.dgv(5, i).Value.ToString()) Then
                        Dim attachedSourcePath As String = xx.dgv(5, i).Value.ToString()
                        Dim attachedDestPath As String = attachedFilesPath & "\" & Path.GetFileName(attachedSourcePath)

                        If File.Exists(attachedSourcePath) Then
                            ' حذف الملف الوجهة إذا كان موجوداً
                            If File.Exists(attachedDestPath) Then
                                File.Delete(attachedDestPath)
                            End If

                            File.Copy(attachedSourcePath, attachedDestPath)
                        Else
                            MessageBox.Show("الملف المرفق غير موجود: " & attachedSourcePath, "خطأ في نسخ الملف المرفق", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                        End If
                    End If
                Next
            End If

        Catch ex As Exception
            MessageBox.Show("خطأ في نسخ الملفات: " & ex.Message, "Error Message", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    ' دالة لتحديث مسار الملف في قاعدة البيانات
    Private Sub UpdateFilePath(ByVal FileID As String, ByVal FilePath As String)
        Try
            Dim Cmd As New SqlCommand
            Cmd.Connection = Con
            Cmd.CommandText = "UPDATE Files SET FilePath = @FilePath WHERE FileID = @FileID"

            Cmd.Parameters.Add("@FilePath", SqlDbType.NVarChar, 500).Value = FilePath
            Cmd.Parameters.Add("@FileID", SqlDbType.Int).Value = CInt(FileID)

            OpenConnection()
            Cmd.ExecuteNonQuery()
            CloseConnection()

        Catch ex As Exception
            MessageBox.Show("خطأ في تحديث مسار الملف: " & ex.Message, "Error Message", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub





#End Region

End Class
