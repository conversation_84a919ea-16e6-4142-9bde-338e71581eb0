-- سكريبت لتحديث الإجراء المخزن InsertFiles لإضافة معامل FilePath
-- يجب تشغيل هذا السكريبت على قاعدة البيانات ArchiSys

USE ArchiSys
GO

-- حذف الإجراء المخزن الحالي
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[InsertFiles]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[InsertFiles]
GO

-- إنشاء الإجراء المخزن المحدث مع معامل FilePath
CREATE PROCEDURE InsertFiles
    @FileCode         DECIMAL,
    @FileName         NVARCHAR(300),
    @FolderParent     DECIMAL,
    @RefrenceNum      NVARCHAR(50),
    @FileDescription  NVARCHAR(MAX),
    @FileExtenssion   VARCHAR(10),
    @FileSize         VARCHAR(20),
    @FileType         TINYINT,
    @FileSecret       BIT,
    @FilePassword     NVARCHAR(25),
    @AddUser          TINYINT,
    @DTAttaches       AttachedFilesType READONLY,
    @FilePath         NVARCHAR(500) = NULL,  -- معامل جديد لمسار الملف
    @ID               INT OUTPUT
AS
BEGIN
    BEGIN TRANSACTION
    BEGIN TRY
        DECLARE @FileID INT
        SELECT @FileID = ISNULL(MAX(FileID), 0) + 1 FROM Files

        -- إدراج الملف الرئيسي مع مسار الملف
        INSERT INTO Files (
            FileID, FileCode, FileName, FolderParent, RefrenceNum,
            FileDescription, FileExtenssion, FileSize, FileType,
            FileSecret, FilePassword, AddUser, AddDate, FilePath
        )
        VALUES (
            @FileID, @FileCode, @FileName, @FolderParent, @RefrenceNum,
            @FileDescription, @FileExtenssion, @FileSize, @FileType,
            @FileSecret, @FilePassword, @AddUser, GETDATE(), @FilePath
        )

        -- إدراج الملفات المرفقة
        INSERT INTO AttachedFiles (ParentID, FileName, FileExtenssion, FileSize, AddDate)
        SELECT @FileID, FileName, FileExtenssion, FileSize, GETDATE()
        FROM @DTAttaches

        COMMIT TRANSACTION
        SET @ID = @FileID
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION
        SET @ID = 0
        -- إرجاع تفاصيل الخطأ
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE()
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY()
        DECLARE @ErrorState INT = ERROR_STATE()
        RAISERROR (@ErrorMessage, @ErrorSeverity, @ErrorState)
    END CATCH
END
GO

-- التحقق من إنشاء الإجراء المخزن بنجاح
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[InsertFiles]') AND type in (N'P', N'PC'))
    PRINT 'تم تحديث الإجراء المخزن InsertFiles بنجاح'
ELSE
    PRINT 'فشل في تحديث الإجراء المخزن InsertFiles'
GO
