﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
	  <TargetFramework>net8.0-windows</TargetFramework>

	  <StartupObject>Sub Main</StartupObject>
    <UseWindowsForms>True</UseWindowsForms>
    <MyType>WindowsForms</MyType>
    <UseWPF>False</UseWPF>
    <Platforms>AnyCPU;x86</Platforms>
  </PropertyGroup>



  <ItemGroup>
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="My Project\Application.Designer.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Update="My Project\Resources.Designer.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Update="My Project\Settings.Designer.vb">
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <None Update="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Update="My Project\Settings.settings">
      <CustomToolNamespace>My</CustomToolNamespace>
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
    <None Update="References\ALquhaliLibrary.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="References\Interop.WIA.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="References\TwainLib.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="References\" />
    <Folder Include="Resources\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="iTextSharp" Version="5.5.13.4" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageReference Include="Microsoft.Web.WebView2" Version="1.0.3296.44" />
    <PackageReference Include="NTwain" Version="3.7.5" />
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="ALquhaliLibrary">
      <HintPath>References\ALquhaliLibrary.dll</HintPath>
      <Private>True</Private>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="Interop.WIA">
      <HintPath>References\Interop.WIA.dll</HintPath>
      <Private>False</Private>
      <SpecificVersion>False</SpecificVersion>
      <EmbedInteropTypes>False</EmbedInteropTypes>
    </Reference>
    <Reference Include="TwainLib">
      <HintPath>References\TwainLib.dll</HintPath>
      <EmbedInteropTypes>False</EmbedInteropTypes>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="My Project\Resources.resx">
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>